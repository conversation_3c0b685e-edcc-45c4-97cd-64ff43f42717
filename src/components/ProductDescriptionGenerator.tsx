'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Trash2, RefreshCcw, Clipboard, FileText, Loader, Plus, ImageIcon, Sparkles } from 'lucide-react';
import '../styles/product-description.css';

// Tipos de dados
interface ProductInfo {
  name: string;
  category: string;
  features: string[];
  keywords: string[];
  targetAudience: string;
  additionalInfo: string;
}

interface SeoContent {
  shortDescription: string;
  slug: string;
  wooCommerceMainDescription: string;
  wooCommerceShortDescription: string;
}

// Componente para exibir um campo de resultado
const ResultField = ({ title, content, onCopy }: { title: string; content: string; onCopy: (text: string, field: string) => void; }) => {
  const charCount = content.length;
  let textColorClass = "text-gray-700 dark:text-gray-300";
  let statusMessage = "";

  if (title === "Descrição SEO") {
    if (charCount >= 140 && charCount <= 160) {
      textColorClass = "text-green-600 dark:text-green-400";
      statusMessage = "✓ Ideal para SEO";
    } else if (charCount >= 120 && charCount < 140) {
      textColorClass = "text-orange-600 dark:text-orange-400";
      statusMessage = "⚠ Muito curta para SEO";
    } else if (charCount > 160 && charCount <= 170) {
      textColorClass = "text-orange-600 dark:text-orange-400";
      statusMessage = "⚠ Ligeiramente longa";
    } else {
      textColorClass = "text-red-600 dark:text-red-400";
      statusMessage = charCount < 120 ? "✗ Muito curta" : "✗ Muito longa para SEO";
    }
  }

  // Verificar se o conteúdo contém HTML (para descrições do WooCommerce)
  const isHtmlContent = title.includes("WooCommerce") && content.includes('<');

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h4 className="text-md font-semibold text-gray-800 dark:text-gray-200">{title}</h4>
        <div className="flex items-center gap-2">
          {(title === "Descrição SEO") && (
            <div className="flex items-center gap-2">
              <span className={`text-xs font-medium ${textColorClass}`}>
                {charCount}/160 caracteres
              </span>
              <span className={`text-xs ${textColorClass}`}>
                {statusMessage}
              </span>
            </div>
          )}
          <Button
            onClick={() => onCopy(content, title)}
            variant="ghost"
            size="sm"
            className="flex items-center gap-1.5 text-xs h-8 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
          >
            <Clipboard className="h-3.5 w-3.5" />
            <span>Copiar</span>
          </Button>
        </div>
      </div>
      <div className={`max-w-none p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700 text-sm font-sans ${isHtmlContent ? 'formatted-content-container' : 'whitespace-pre-wrap'}`}>
        {isHtmlContent ? (
          <div
            dangerouslySetInnerHTML={{ __html: content }}
            className="formatted-content"
          />
        ) : (
          <div className="simple-content">
            {content}
          </div>
        )}
      </div>

    </div>
  );
};

const ProductDescriptionGenerator = () => {
  // Estados
  const [productInfo, setProductInfo] = useState<ProductInfo>({
    name: '',
    category: '',
    features: [''],
    keywords: [''],
    targetAudience: '',
    additionalInfo: ''
  });
  const [currentDescription, setCurrentDescription] = useState('');
  const [generatedContent, setGeneratedContent] = useState<SeoContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isKeywordsLoading, setIsKeywordsLoading] = useState(false);
  const descriptionSectionRef = useRef<HTMLDivElement>(null);

  // Efeitos para LocalStorage
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('productDescriptionFormData');
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setProductInfo(parsedData.productInfo || { name: '', category: '', features: [''], keywords: [''], targetAudience: '', additionalInfo: '' });
        setCurrentDescription(parsedData.currentDescription || '');
      }
    } catch (error) {
      console.error("Failed to load data from localStorage", error);
    }
  }, []);

  useEffect(() => {
    try {
      const dataToSave = JSON.stringify({ productInfo, currentDescription });
      localStorage.setItem('productDescriptionFormData', dataToSave);
    } catch (error) {
      console.error("Failed to save data to localStorage", error);
    }
  }, [productInfo, currentDescription]);

  // Manipuladores de API
  const handleApiCall = async (action: 'generate' | 'improve') => {
    if (action === 'generate' && !productInfo.name) {
      toast.error('O nome do produto é obrigatório.');
      return;
    }
    if (action === 'improve' && !currentDescription) {
      toast.error('A descrição atual é obrigatória para a poder melhorar.');
      return;
    }

    setIsLoading(true);
    const toastId = toast.loading(`A ${action === 'generate' ? 'gerar' : 'melhorar'} conteúdo SEO...`);

    try {
      const response = await fetch('/api/generate-description', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          productInfo: {
            ...productInfo,
            features: productInfo.features.filter(f => f.trim() !== ''),
            keywords: productInfo.keywords.filter(k => k.trim() !== '')
          },
          currentDescription
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Ocorreu um erro desconhecido.');

      setGeneratedContent(data.seoContent);
      toast.success('Conteúdo SEO gerado com sucesso!', { id: toastId });

      setTimeout(() => {
        descriptionSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

    } catch (error: unknown) {
      console.error(`Erro ao ${action} descrição:`, error);

      let errorMessage = 'Erro desconhecido';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide more user-friendly messages for common errors
        if (errorMessage.includes('quota')) {
          errorMessage = 'Quota da API excedida. Por favor, tente novamente mais tarde ou contacte o suporte.';
        } else if (errorMessage.includes('401') || errorMessage.includes('inválida')) {
          errorMessage = 'Problema de autenticação com a API. Por favor, contacte o suporte.';
        } else if (errorMessage.includes('503') || errorMessage.includes('indisponível')) {
          errorMessage = 'Serviço temporariamente indisponível. Tente novamente em alguns minutos.';
        } else if (errorMessage.includes('400') || errorMessage.includes('inválido')) {
          errorMessage = 'Dados inválidos. Verifique as informações do produto e tente novamente.';
        } else if (errorMessage.includes('JSON')) {
          errorMessage = 'Resposta inválida do servidor. Tente novamente.';
        }
      }

      toast.error(`Falha ao ${action === 'generate' ? 'gerar' : 'melhorar'} conteúdo. ${errorMessage}`, { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestKeywords = async () => {
    if (!productInfo.name) {
      toast.error('Por favor, insira o nome do produto para sugerir palavras-chave.');
      return;
    }

    setIsKeywordsLoading(true);
    const toastId = toast.loading('A sugerir palavras-chave...');

    try {
      const response = await fetch('/api/suggest-keywords', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productName: productInfo.name,
          productCategory: productInfo.category
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Ocorreu um erro ao sugerir palavras-chave.');

      if (data.keywords && data.keywords.length > 0) {
        setProductInfo(prev => ({
          ...prev,
          keywords: data.keywords.slice(0, 3) // Limita a 3 palavras-chave
        }));
        toast.success('Palavras-chave sugeridas com sucesso!', { id: toastId });
      } else {
        toast.info('Nenhuma palavra-chave sugerida. Tente ser mais específico.', { id: toastId });
      }

    } catch (error: unknown) {
      console.error('Erro ao sugerir palavras-chave:', error);

      let errorMessage = 'Erro desconhecido';

      if (error instanceof Error) {
        errorMessage = error.message;

        // Provide more user-friendly messages for common errors
        if (errorMessage.includes('quota')) {
          errorMessage = 'Quota da API excedida. Tente novamente mais tarde.';
        } else if (errorMessage.includes('401') || errorMessage.includes('inválida')) {
          errorMessage = 'Problema de autenticação. Contacte o suporte.';
        } else if (errorMessage.includes('503') || errorMessage.includes('indisponível')) {
          errorMessage = 'Serviço temporariamente indisponível. Tente novamente em alguns minutos.';
        }
      }

      toast.error(`Falha ao sugerir palavras-chave. ${errorMessage}`, { id: toastId });
    } finally {
      setIsKeywordsLoading(false);
    }
  };



  // Enhanced function to correct common Portuguese text errors
  const correctTextErrors = (text: string): string => {
    if (!text) return '';

    let corrected = text;

    // Fix spacing issues
    corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
    corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
    corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

    // Fix capitalization after punctuation
    corrected = corrected.replace(/([.!?])\s+([a-z])/g, (_, punct, letter) => {
      return punct + ' ' + letter.toUpperCase();
    });

    // Comprehensive Portuguese spelling and grammar corrections
    const corrections: { [key: string]: string } = {
      // Common spelling errors
      'qualidadde': 'qualidade',
      'funcionalidadde': 'funcionalidade',
      'resistênte': 'resistente',
      'duravél': 'durável',
      'electrónico': 'eletrónico',
      'electrónicos': 'eletrónicos',
      'electrónica': 'eletrónica',
      'optimizar': 'otimizar',
      'optimizado': 'otimizado',
      'optimização': 'otimização',

      // Accent corrections
      'facil': 'fácil',
      'util': 'útil',
      'pratico': 'prático',
      'economico': 'económico',
      'tecnologico': 'tecnológico',
      'automatico': 'automático',
      'ergonomico': 'ergonómico',
      'comodo': 'cómodo',
      'rapido': 'rápido',
      'solido': 'sólido',
      'versatil': 'versátil',
      'portatil': 'portátil',
      'flexivel': 'flexível',
      'resistente': 'resistente',
      'eficiente': 'eficiente',
      'elegante': 'elegante',
      'moderno': 'moderno',

      // Gender concordance corrections (common mistakes)
      'uma produto': 'um produto',
      'esta produto': 'este produto',
      'essa produto': 'esse produto',
      'aquela produto': 'aquele produto',
      'o qualidade': 'a qualidade',
      'este qualidade': 'esta qualidade',
      'esse qualidade': 'essa qualidade',
      'aquele qualidade': 'aquela qualidade',

      // Portuguese vs Brazilian terms
      'celular': 'telemóvel',
      'mouse': 'rato',
      'deletar': 'eliminar',
      'salvar': 'guardar',
      'tela': 'ecrã',
      'aplicativo': 'aplicação',
      'usuário': 'utilizador',
      'usuários': 'utilizadores',

      // Common adjective errors
      'ajustável de alta qualidade': 'ajustável e de alta qualidade',
      'resistente de qualidade': 'resistente e de qualidade',
      'durável de alta': 'durável e de alta',
      'prático de uso': 'prático no uso',
      'fácil de usar': 'fácil de usar',

      // Preposition corrections
      'ideal para quem busca': 'ideal para quem procura',
      'perfeito para quem quer': 'perfeito para quem deseja',
      'ótimo para quem precisa': 'excelente para quem necessita',

      // Common word corrections
      'ótimo': 'excelente',
      'legal': 'fantástico',
      'bacana': 'interessante',
      'maneiro': 'interessante'
    };

    // Apply corrections
    Object.entries(corrections).forEach(([wrong, right]) => {
      const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
      corrected = corrected.replace(regex, right);
    });

    // Fix common HTML formatting issues in Portuguese
    corrected = corrected.replace(/<strong>([^<]+)<\/strong>/g, (_, content) => {
      return `<strong>${content.trim()}</strong>`;
    });

    // Ensure proper spacing around HTML tags
    corrected = corrected.replace(/>\s+</g, '><');
    corrected = corrected.replace(/\s+>/g, '>');
    corrected = corrected.replace(/<\s+/g, '<');

    return corrected.trim();
  };

  // Enhanced UI handlers with automatic error correction
  const handleCopy = (text: string, fieldName: string) => {
    if (!text) return;

    // Check if content contains HTML (for WooCommerce descriptions)
    // Both main and short WooCommerce descriptions should be copied as HTML
    const isHtmlContent = fieldName.includes("WooCommerce") && text.includes('<');

    // Apply error correction and keep appropriate format
    let textToCopy = text;

    if (isHtmlContent) {
      // Keep HTML format for WooCommerce descriptions - apply error correction to HTML
      textToCopy = correctTextErrors(text);
      // Clean up HTML formatting for better clipboard compatibility
      textToCopy = textToCopy
        .replace(/>\s+</g, '><') // Remove extra spaces between tags
        .replace(/\n\s*\n/g, '\n') // Remove extra line breaks
        .trim();
    } else {
      // Apply error correction to plain text
      textToCopy = correctTextErrors(text);
      // For plain text, ensure consistent formatting
      textToCopy = textToCopy
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Max two line breaks
        .trim();
    }

    navigator.clipboard.writeText(textToCopy)
      .then(() => toast.success(`${fieldName} copiado para a área de transferência!`))
      .catch(() => toast.error('Falha ao copiar.'));
  };

  const handleReset = () => {
    try {
      localStorage.removeItem('productDescriptionFormData');
    } catch (error) {
      console.error("Failed to clear localStorage", error);
    }
    setGeneratedContent(null);
    setProductInfo({ name: '', category: '', features: [''], keywords: [''], targetAudience: '', additionalInfo: '' });
    setCurrentDescription('');
    toast.success('Formulário limpo e pronto para um novo produto!');
  };

  // Manipuladores de formulário
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProductInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleArrayInputChange = (index: number, value: string, field: 'features' | 'keywords') => {
    const newArray = [...productInfo[field]];
    newArray[index] = value;
    setProductInfo(prev => ({ ...prev, [field]: newArray }));
  };

  const addArrayItem = (field: 'features' | 'keywords') => {
    setProductInfo(prev => ({ ...prev, [field]: [...prev[field], ''] }));
  };

  const removeArrayItem = (index: number, field: 'features' | 'keywords') => {
    if (productInfo[field].length <= 1) return;
    const newArray = productInfo[field].filter((_, i) => i !== index);
    setProductInfo(prev => ({ ...prev, [field]: newArray }));
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
      {/* Coluna do Formulário */}
      <div className="lg:col-span-5 space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <Tabs defaultValue="generate" className="space-y-4">
            <div className="flex flex-col gap-3 mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Gerador de Conteúdo SEO</h3>
              <TabsList className="grid grid-cols-2 w-full">
                <TabsTrigger value="generate">Novo Produto</TabsTrigger>
                <TabsTrigger value="improve">Melhorar Texto</TabsTrigger>
              </TabsList>
            </div>

            {/* Aba de Geração */}
            <TabsContent value="generate" className="space-y-5">
                <div className="space-y-3">
                    <div className="flex items-center justify-between">
                        <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">Nome do Produto <span className="text-red-500">*</span></label>
                        <span className="text-xs text-gray-500 dark:text-gray-400">Obrigatório</span>
                    </div>
                    <Input id="name" name="name" value={productInfo.name} onChange={handleInputChange} placeholder="Ex: Sapatilhas de Corrida Leves" />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div className="space-y-2">
                        <label htmlFor="category" className="text-sm font-medium text-gray-700 dark:text-gray-300">Categoria</label>
                        <Input id="category" name="category" value={productInfo.category} onChange={handleInputChange} placeholder="Ex: Calçado Desportivo" />
                    </div>
                    <div className="space-y-2">
                        <label htmlFor="targetAudience" className="text-sm font-medium text-gray-700 dark:text-gray-300">Público-alvo</label>
                        <Input id="targetAudience" name="targetAudience" value={productInfo.targetAudience} onChange={handleInputChange} placeholder="Ex: Corredores amadores" />
                    </div>
                </div>

                <div className="space-y-3 pt-2">
                    <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Características Principais</label>
                        <span className="text-xs text-gray-500 dark:text-gray-400">máx. 3 principais</span>
                    </div>
                    {productInfo.features.slice(0, 3).map((feature, index) => (
                        <div key={`feature-${index}`} className="flex gap-2">
                            <Input value={feature} onChange={(e) => handleArrayInputChange(index, e.target.value, 'features')} placeholder={`Ex: Sola com amortecimento reativo`} />
                            <Button type="button" variant="destructive" size="icon" onClick={() => removeArrayItem(index, 'features')} disabled={productInfo.features.length <= 1}><Trash2 className="h-4 w-4" /></Button>
                        </div>
                    ))}
                    {productInfo.features.length < 3 && <Button type="button" variant="outline" size="sm" onClick={() => addArrayItem('features')}><Plus className="h-4 w-4 mr-2" />Adicionar Característica</Button>}
                </div>

                <div className="space-y-3 pt-2">
                    <div className="flex items-center justify-between">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Palavras-chave SEO</label>
                        <Button 
                            type="button" 
                            variant="outline" 
                            size="sm" 
                            onClick={handleSuggestKeywords}
                            disabled={isKeywordsLoading || !productInfo.name}
                            className="flex items-center gap-1 text-xs h-8"
                        >
                            {isKeywordsLoading ? (
                                <Loader className="h-3.5 w-3.5 animate-spin" />
                            ) : (
                                <Sparkles className="h-3.5 w-3.5" />
                            )}
                            <span>Sugerir com IA</span>
                        </Button>
                    </div>
                    {productInfo.keywords.slice(0, 3).map((keyword, index) => (
                        <div key={`keyword-${index}`} className="flex gap-2">
                            <Input value={keyword} onChange={(e) => handleArrayInputChange(index, e.target.value, 'keywords')} placeholder={`Ex: sapatilhas de corrida`} />
                            <Button type="button" variant="destructive" size="icon" onClick={() => removeArrayItem(index, 'keywords')} disabled={productInfo.keywords.length <= 1}><Trash2 className="h-4 w-4" /></Button>
                        </div>
                    ))}
                    {productInfo.keywords.length < 3 && <Button type="button" variant="outline" size="sm" onClick={() => addArrayItem('keywords')}><Plus className="h-4 w-4 mr-2" />Adicionar Palavra-chave</Button>}
                </div>

                <div className="space-y-2 pt-2">
                    <label htmlFor="additionalInfo" className="text-sm font-medium text-gray-700 dark:text-gray-300">Informações Adicionais</label>
                    <Textarea id="additionalInfo" name="additionalInfo" value={productInfo.additionalInfo} onChange={handleInputChange} placeholder="Detalhes sobre o material, tecnologia, ou benefícios específicos..." rows={3} />
                </div>

                <Button onClick={() => handleApiCall('generate')} disabled={isLoading || !productInfo.name} className="w-full h-12 mt-4 text-base">
                    {isLoading ? <><Loader className="mr-2 h-4 w-4 animate-spin" />A gerar...</> : <><FileText className="mr-2 h-5 w-5" />Gerar Conteúdo SEO</>}
                </Button>
            </TabsContent>

            {/* Aba de Melhoria */}
            <TabsContent value="improve" className="space-y-5">
              <Textarea
                id="currentDescription"
                value={currentDescription}
                onChange={(e) => setCurrentDescription(e.target.value)}
                placeholder="Cole aqui a descrição atual do produto que deseja melhorar..."
                rows={8}
                className="w-full border-gray-200 dark:border-gray-700"
              />
              <Button onClick={() => handleApiCall('improve')} disabled={isLoading || !currentDescription} className="w-full h-12 mt-4 text-base">
                {isLoading ? <><Loader className="mr-2 h-4 w-4 animate-spin" />A melhorar...</> : <><RefreshCcw className="mr-2 h-5 w-5" />Melhorar Conteúdo</>}
              </Button>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Coluna de Resultados */}
      <div ref={descriptionSectionRef} className="lg:col-span-7">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm sticky top-6">
          <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Conteúdo Gerado</h3>
            <Button onClick={handleReset} variant="outline" size="sm" className="flex items-center gap-1.5 h-9 text-xs">
              <Trash2 className="h-3.5 w-3.5" />
              <span>Limpar Tudo</span>
            </Button>
          </div>
          
          <div className="p-6 space-y-6 max-h-[80vh] overflow-y-auto">
            {generatedContent ? (
              <>
                <ResultField title="Descrição WooCommerce" content={generatedContent.wooCommerceMainDescription} onCopy={handleCopy} />
                <ResultField title="Curta Descrição WooCommerce" content={generatedContent.wooCommerceShortDescription} onCopy={handleCopy} />
                <ResultField title="Descrição SEO" content={generatedContent.shortDescription} onCopy={handleCopy} />
                <ResultField title="Slug" content={generatedContent.slug} onCopy={handleCopy} />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center text-center p-10 h-full min-h-[400px]">
                <div className="w-16 h-16 mb-6 rounded-full bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-3">A aguardar informações</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md">
                  Preencha os dados do produto para gerar uma descrição principal, uma meta description para SEO e um slug de URL otimizado.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDescriptionGenerator;
